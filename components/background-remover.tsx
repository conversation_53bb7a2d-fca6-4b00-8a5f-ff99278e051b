'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Turnstile } from '@marsidev/react-turnstile';
import type { TurnstileInstance } from '@marsidev/react-turnstile';
import { toast } from 'sonner';
import { Loader2, Download, Upload, RefreshCw } from 'lucide-react';
import Image from 'next/image';
import ReactCompareImage from 'react-compare-image';

interface BackgroundRemovalResult {
  taskId: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  originalImageUrl: string;
  processedImageUrl?: string;
  createdAt: string;
  completedAt?: string;
}

export default function BackgroundRemover() {
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<BackgroundRemovalResult | null>(null);
  const [turnstileToken, setTurnstileToken] = useState<string | null>(null);
  const [siteKey, setSiteKey] = useState<string>('');
  
  const turnstileRef = useRef<TurnstileInstance | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 获取Turnstile site key
  useEffect(() => {
    fetch('/api/turnstile')
      .then(res => res.json())
      .then(data => setSiteKey(data.siteKey))
      .catch(err => console.error('Failed to get site key:', err));
  }, []);

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    if (!file.type.startsWith('image/')) {
      toast.error('请选择图片文件');
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      toast.error('图片大小不能超过5MB');
      return;
    }

    setIsUploading(true);
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });
      
      const uploadResult = await response.json();
      
      if (uploadResult.status === 'ok') {
        setUploadedImage(uploadResult.urls[0]);
        setResult(null); // 清除之前的结果
        toast.success('图片上传成功');
      } else {
        toast.error(uploadResult.error || '上传失败');
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('上传失败');
    } finally {
      setIsUploading(false);
    }
  };

  // 处理拖拽上传
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  // 处理点击上传
  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  // 开始背景去除
  const handleRemoveBackground = async () => {
    if (!uploadedImage) {
      toast.error('请先上传图片');
      return;
    }

    if (!turnstileToken) {
      toast.error('请完成人机验证');
      return;
    }

    setIsProcessing(true);

    try {
      const response = await fetch('/api/background-remove', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: uploadedImage,
          turnstileToken: turnstileToken
        }),
      });

      const data = await response.json();

      if (data.status === 'OK') {
        toast.success('任务已提交，正在处理中...');
        // 开始轮询状态
        pollTaskStatus(data.taskId);
      } else {
        toast.error(data.error || '处理失败');
        setIsProcessing(false);
      }
    } catch (error) {
      console.error('Background removal error:', error);
      toast.error('处理失败');
      setIsProcessing(false);
    }
  };

  // 轮询任务状态
  const pollTaskStatus = async (taskId: string) => {
    const maxAttempts = 60; // 最多轮询60次（5分钟）
    let attempts = 0;

    const poll = async () => {
      try {
        const response = await fetch('/api/background-remove/status', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ taskId }),
        });

        const data = await response.json();

        if (data.status === 'COMPLETED') {
          setResult(data);
          setIsProcessing(false);
          toast.success('背景去除完成！');
          return;
        } else if (data.status === 'FAILED') {
          setIsProcessing(false);
          toast.error('处理失败');
          return;
        }

        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // 5秒后再次轮询
        } else {
          setIsProcessing(false);
          toast.error('处理超时，请稍后重试');
        }
      } catch (error) {
        console.error('Status polling error:', error);
        setIsProcessing(false);
        toast.error('状态查询失败');
      }
    };

    poll();
  };

  // 下载图片
  const handleDownload = async (imageUrl: string) => {
    try {
      const response = await fetch(`/api/download?url=${encodeURIComponent(imageUrl)}`);
      const blob = await response.blob();
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `background-removed-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('图片下载成功');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('下载失败');
    }
  };

  // 重置Turnstile
  const resetTurnstile = () => {
    turnstileRef.current?.reset();
    setTurnstileToken(null);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">
            AI 背景去除工具
          </CardTitle>
          <p className="text-center text-muted-foreground">
            使用先进的AI技术，一键去除图片背景
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 图片上传区域 */}
          <div
            className="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 dark:hover:border-gray-600 transition-colors"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={handleUploadClick}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="hidden"
            />
            
            {isUploading ? (
              <div className="flex flex-col items-center space-y-2">
                <Loader2 className="h-8 w-8 animate-spin" />
                <p>上传中...</p>
              </div>
            ) : uploadedImage ? (
              <div className="space-y-4">
                <Image
                  src={uploadedImage}
                  alt="Uploaded image"
                  width={300}
                  height={200}
                  className="mx-auto rounded-lg object-contain"
                />
                <p className="text-sm text-muted-foreground">
                  点击重新上传图片
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <Upload className="h-12 w-12 mx-auto text-gray-400" />
                <div>
                  <p className="text-lg font-medium">点击上传图片</p>
                  <p className="text-sm text-muted-foreground">
                    或拖拽图片到此处 (最大5MB)
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Turnstile验证 */}
          {uploadedImage && siteKey && (
            <div className="flex flex-col items-center space-y-4">
              <div className="flex items-center space-x-2">
                <Turnstile
                  ref={turnstileRef}
                  siteKey={siteKey}
                  onSuccess={setTurnstileToken}
                  onError={() => {
                    toast.error('验证失败，请重试');
                    setTurnstileToken(null);
                  }}
                  onExpire={() => {
                    toast.warning('验证已过期，请重新验证');
                    setTurnstileToken(null);
                  }}
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetTurnstile}
                  className="ml-2"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
              
              <Button
                onClick={handleRemoveBackground}
                disabled={!turnstileToken || isProcessing}
                className="w-full max-w-md"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    处理中...
                  </>
                ) : (
                  '开始去除背景'
                )}
              </Button>
            </div>
          )}

          {/* 结果展示 */}
          {result && result.processedImageUrl && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-center">处理结果</h3>
              
              <div className="max-w-2xl mx-auto">
                <ReactCompareImage
                  leftImage={result.originalImageUrl}
                  rightImage={result.processedImageUrl}
                  leftImageLabel="原图"
                  rightImageLabel="去除背景后"
                />
              </div>
              
              <div className="flex justify-center space-x-4">
                <Button
                  onClick={() => handleDownload(result.processedImageUrl!)}
                  className="flex items-center space-x-2"
                >
                  <Download className="h-4 w-4" />
                  <span>下载处理后的图片</span>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
