import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { uploadImageFromUrlToR2 } from "@/lib/storage";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      id,
      status,
      output,
      error
    } = body;

    console.log("Replicate Background Removal Webhook received:", body);

    // 查找背景去除记录
    const backgroundRemoval = await prisma.backgroundRemoval.findUnique({
      where: { id }
    });

    if (!backgroundRemoval) {
      console.error("Background removal not found for id:", id);
      return NextResponse.json({ error: "Background removal not found" }, { status: 404 });
    }

    // 更新状态和元数据
    const updateData: any = {
      metadata: body,
      updatedAt: new Date()
    };

    if (status === "succeeded" && output) {
      updateData.status = "COMPLETED";
      updateData.processedImageUrl = output;
      updateData.completedAt = new Date();

      // 将处理后的图片上传到R2存储
      try {
        const key = `background-removed/${id}`;
        const storagePath = await uploadImageFromUrlToR2(key, output);
        if (storagePath) {
          updateData.storagePath = storagePath;
        }
      } catch (uploadError) {
        console.error("Error uploading processed image to R2:", uploadError);
        // 继续执行，不因为上传失败而中断
      }
    } else if (status === "failed" || error) {
      updateData.status = "FAILED";
      updateData.completedAt = new Date();
    } else if (status === "starting" || status === "processing") {
      updateData.status = "PENDING";
    }

    // 更新数据库记录
    await prisma.backgroundRemoval.update({
      where: { id },
      data: updateData
    });

    return NextResponse.json({ success: true });
  } catch (err) {
    console.error("Error processing Replicate background removal webhook:", err);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
