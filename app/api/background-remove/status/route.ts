import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const body = await request.json();
    const { taskId } = body;

    if (!taskId) {
      return NextResponse.json({ error: '缺少任务ID' }, { status: 400 });
    }

    // 查找背景去除记录
    const backgroundRemoval = await prisma.backgroundRemoval.findUnique({
      where: { 
        id: taskId,
        userId: session.user.id // 确保只能查询自己的任务
      }
    });

    if (!backgroundRemoval) {
      return NextResponse.json({ error: '任务不存在' }, { status: 404 });
    }

    return NextResponse.json({
      taskId: backgroundRemoval.id,
      status: backgroundRemoval.status,
      originalImageUrl: backgroundRemoval.originalImageUrl,
      processedImageUrl: backgroundRemoval.processedImageUrl,
      createdAt: backgroundRemoval.createdAt,
      completedAt: backgroundRemoval.completedAt
    });

  } catch (error) {
    console.error('Status check error:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
