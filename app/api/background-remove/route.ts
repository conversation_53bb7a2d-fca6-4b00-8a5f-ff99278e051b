import { NextRequest, NextResponse } from "next/server";
import Replicate from "replicate";

// 验证Turnstile token
async function verifyTurnstileToken(token: string): Promise<boolean> {
  const verifyEndpoint = 'https://challenges.cloudflare.com/turnstile/v0/siteverify';
  const secret = process.env.TURNSTILE_SECRET_KEY;

  if (!secret) {
    console.error('TURNSTILE_SECRET_KEY not configured');
    return false;
  }

  try {
    const response = await fetch(verifyEndpoint, {
      method: 'POST',
      body: `secret=${encodeURIComponent(secret)}&response=${encodeURIComponent(token)}`,
      headers: {
        'content-type': 'application/x-www-form-urlencoded'
      }
    });

    const data = await response.json();
    return data.success === true;
  } catch (error) {
    console.error('Error verifying Turnstile token:', error);
    return false;
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { imageUrl, turnstileToken } = body;

    if (!imageUrl || !turnstileToken) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }

    // 验证Turnstile token
    const isValidToken = await verifyTurnstileToken(turnstileToken);
    if (!isValidToken) {
      return NextResponse.json({ error: 'Turnstile验证失败' }, { status: 400 });
    }

    // 调用Replicate API - 同步等待结果
    const replicate = new Replicate({
      auth: process.env.REPLICATE_API_TOKEN,
    });

    console.log('Starting background removal for image:', imageUrl);

    const output = await replicate.run(
      "851-labs/background-remover:a029dff38972b5fda4ec5d75d7d1cd25aeff621d2cf4946a41055d7db66b80bc",
      {
        input: {
          image: imageUrl,
          format: 'png',
          reverse: false,
          threshold: 0,
          background_type: 'rgba'
        }
      }
    );

    console.log('Background removal completed:', output);

    if (!output) {
      return NextResponse.json({ error: '背景去除失败' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      originalImageUrl: imageUrl,
      processedImageUrl: output
    });

  } catch (error) {
    console.error('Background removal error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : '服务器内部错误'
    }, { status: 500 });
  }
}
