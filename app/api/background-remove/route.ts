import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import Replicate from "replicate";

// 验证Turnstile token
async function verifyTurnstileToken(token: string): Promise<boolean> {
  const verifyEndpoint = 'https://challenges.cloudflare.com/turnstile/v0/siteverify';
  const secret = process.env.TURNSTILE_SECRET_KEY;

  if (!secret) {
    console.error('TURNSTILE_SECRET_KEY not configured');
    return false;
  }

  try {
    const response = await fetch(verifyEndpoint, {
      method: 'POST',
      body: `secret=${encodeURIComponent(secret)}&response=${encodeURIComponent(token)}`,
      headers: {
        'content-type': 'application/x-www-form-urlencoded'
      }
    });

    const data = await response.json();
    return data.success === true;
  } catch (error) {
    console.error('Error verifying Turnstile token:', error);
    return false;
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const body = await request.json();
    const { imageUrl, turnstileToken } = body;

    if (!imageUrl || !turnstileToken) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }

    // 验证Turnstile token
    const isValidToken = await verifyTurnstileToken(turnstileToken);
    if (!isValidToken) {
      return NextResponse.json({ error: 'Turnstile验证失败' }, { status: 400 });
    }

    // 检查用户积分
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { subscription: true }
    });

    if (!user?.subscription || user.subscription.creditsRemaining < 5) {
      return NextResponse.json({ error: '积分不足' }, { status: 400 });
    }

    // 调用Replicate API
    const replicate = new Replicate({
      auth: process.env.REPLICATE_API_TOKEN,
    });

    const webhook = `${process.env.NEXTAUTH_URL}/api/callback/replicate-background`;
    
    const prediction = await replicate.predictions.create({
      model: "851-labs/background-remover",
      input: {
        image: imageUrl
      },
      webhook,
      webhook_events_filter: ["start", "completed"]
    });

    if (!prediction?.id) {
      return NextResponse.json({ error: '创建任务失败' }, { status: 500 });
    }

    // 创建背景去除记录
    const backgroundRemoval = await prisma.backgroundRemoval.create({
      data: {
        id: prediction.id,
        userId: session.user.id,
        originalImageUrl: imageUrl,
        status: 'PENDING',
        creditsUsed: 5,
        metadata: prediction
      }
    });

    // 扣除积分
    await prisma.subscription.update({
      where: { userId: session.user.id },
      data: {
        creditsRemaining: {
          decrement: 5
        }
      }
    });

    return NextResponse.json({
      taskId: prediction.id,
      status: 'OK'
    });

  } catch (error) {
    console.error('Background removal error:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
