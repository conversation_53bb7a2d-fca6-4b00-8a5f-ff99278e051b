// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String          @id @default(cuid())
  name          String?
  email         String          @unique
  passwordHash  String?
  emailVerified DateTime?
  image         String?
  isActive      Boolean         @default(true)
  accounts      Account[]
  sessions      Session[]
  // Optional for WebAuthn support
  Authenticator Authenticator[]
  //subscription
  subscription  Subscription?

  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  ImageGeneration ImageGeneration[]
  Image           Image[]
  EditorMulti     EditorMulti[]
  EditorMultiImage EditorMultiImage[]
  BackgroundRemoval BackgroundRemoval[]
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
}

// Optional for WebAuthn support
model Authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
}

model Subscription {
  id                     String             @id @default(cuid())
  userId                 String             @unique // Ensures one active subscription per user
  user                   User               @relation(fields: [userId], references: [id])
  type                   SubscriptionType // Existing enum: FREE, PREMIUM
  status                 SubscriptionStatus @default(INACTIVE)
  creditsGrantedPerMonth Int                @default(0) // Credits granted by this plan each month/period
  creditsRemaining       Int                @default(0) // Current available credits for the user

  currentPeriodStart    DateTime? // When the current billing cycle started
  currentPeriodEnd      DateTime? // When the current billing cycle ends (and renews or expires)
  trialEndsAt           DateTime? // If the subscription is a trial, when it ends
  // Optional: For integrating with payment providers like Stripe
  stripeSubscriptionId  String?   @unique
  stripeCustomerId      String?   @unique
  stripePaymentMethodId String?
  stripePriceId         String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum SubscriptionStatus {
  ACTIVE // Subscription is active and paid
  CANCELED // User initiated cancellation, active until period end
  INACTIVE // Subscription has ended or was never active
  PAST_DUE // Payment failed, services might be restricted
  TRIALING // User is in a free trial period
}

enum SubscriptionType {
  FREE
  PREMIUM
}

enum GenerationStatus {
  INIT
  PENDING
  COMPLETED
  FAILED
}

// 图片生成记录表
model ImageGeneration {
  id             String           @id @default(cuid())
  userId         String // 外键字段
  user           User             @relation(fields: [userId], references: [id])
  userPrompt     String
  prompt         String
  negativePrompt String?
  taskId         String?
  modelId        String
  parameters     Json // 存储宽度、高度、种子值等
  status         GenerationStatus @default(PENDING)
  createdAt      DateTime         @default(now())
  completedAt    DateTime?
  creditsUsed    Int
  metadata       Json?

  model  Model   @relation(fields: [modelId], references: [id])
  images Image[] // 本次生成产生的图片
}

// 图片表
model Image {
  id           String          @id @default(cuid())
  generationId String // 外键字段
  userId       String // 外键字段
  user         User            @relation(fields: [userId], references: [id])
  imageUrl     String
  thumbnailUrl String?
  storagePath  String?
  isFavorite   Boolean         @default(false) // 默认不是收藏的
  generation   ImageGeneration @relation(fields: [generationId], references: [id])
}

enum ModelCategory {
  TEXT_TO_IMAGE
  IMAGE_TO_IMAGE
  IMAGE_EDIT
  IMAGE_ENHANCEMENT
  IMAGE_TO_TEXT
  TEXT_TO_VIDEO
  IMAGE_TO_VIDEO
  VIDEO_TO_VIDEO
}

enum ModelType {
  //普通，高级模型
  STANDARD
  PREMIUM
}

// 模型表
model Model {
  id          String        @id @default(cuid())
  modelName   String // 模型名称
  modelId     String        @unique // 模型ID
  category    ModelCategory // 模型类别
  provider    String // 提供商
  description String?
  isAvailable Boolean       @default(true) // 默认可用
  creditCost  Int // 使用成本
  parameters  Json? // 支持的参数，可选
  type        ModelType @default(STANDARD) // 模型类型，默认是普通模型
  generations ImageGeneration[] // 使用此模型生成的记录

}

//multi images editor

model EditorMulti {
  id         String           @id @default(cuid())
  userId     String // 外键字段
  taskId     String? // 任务ID
  prompt     String
  parameters Json? // 支持的参数，可选
  userPrompt String?
  imageUrls  String[] // 多张图片的URL
  creditsUsed Int
  metadata Json?
  status     GenerationStatus @default(PENDING)
  completedAt DateTime?
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt

  user        User          @relation(fields: [userId], references: [id])
  EditorMultiImage EditorMultiImage[]
}

model EditorMultiImage {
  id       String @id @default(cuid())
  editorId String // 外键字段
  userId   String // 外键字段
  imageUrl String
  tmpUrl   String?
  user     User   @relation(fields: [userId], references: [id])
  editor   EditorMulti @relation(fields: [editorId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 背景去除表
model BackgroundRemoval {
  id                 String           @id // 使用Replicate prediction ID
  userId             String
  user               User             @relation(fields: [userId], references: [id])
  originalImageUrl   String
  processedImageUrl  String?
  storagePath        String?
  status             GenerationStatus @default(PENDING)
  creditsUsed        Int
  metadata           Json?
  createdAt          DateTime         @default(now())
  completedAt        DateTime?
  updatedAt          DateTime         @updatedAt
}

// 调试表
model Debug {
  id     Int @id @default(autoincrement())
  data Json
  createdAt DateTime @default(now())
}
